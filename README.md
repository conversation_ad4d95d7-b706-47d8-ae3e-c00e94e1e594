# Glyphic Sales Agent - Cloud Run Deployment

## Deploy to Google Cloud Run (2 Commands)

```bash
# 1. Build and push to Google Container Registry
gcloud builds submit --tag gcr.io/gemini-playground-415910/glyphic-sales-agent

# 2. Deploy to Cloud Run with OpenAI API key
gcloud run deploy glyphic-sales-agent \
  --image gcr.io/gemini-playground-415910/glyphic-sales-agent \
  --region europe-west1  \
  --platform managed \
  --set-env-vars OPENAI_API_KEY=******************************************************************************************************************************************************************** \
  --allow-unauthenticated \
  --port 8080
```

<!-- gcloud builds submit --tag gcr.io/gemini-playground-415910/glyphic-sales-agent -->

> Replace `PROJECT_ID` with your Google Cloud project ID and `your_openai_api_key_here` with your actual OpenAI API key.

## Access Your App

After deployment, Cloud Run will provide a URL like:
`https://glyphic-sales-agent-xxxxx-uc.a.run.app`

Click **Connect** to start talking with the AI sales agent!

## Local Development

```bash
docker build -t glyphic-sales-agent .
docker run -p 8080:8080 -e OPENAI_API_KEY=your_key glyphic-sales-agent
```

Open: http://localhost:8080

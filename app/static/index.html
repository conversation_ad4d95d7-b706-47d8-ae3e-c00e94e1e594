<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Realtime Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: white;
            padding: 1rem;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .connect-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        
        .connect-btn.disconnected {
            background: #0066cc;
            color: white;
        }
        
        .connect-btn.connected {
            background: #dc3545;
            color: white;
        }
        
        .connect-btn:hover {
            opacity: 0.9;
        }
        
        .main {
            flex: 1;
            display: flex;
            gap: 1rem;
            padding: 1rem;
            height: calc(100vh - 80px);
        }
        
        .messages-pane {
            flex: 2;
            background: white;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .messages-header {
            padding: 1rem;
            border-bottom: 1px solid #e1e5e9;
            font-weight: 600;
        }
        
        .messages-content {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }
        
        .message {
            margin-bottom: 1rem;
            display: flex;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message.assistant {
            justify-content: flex-start;
        }
        
        .message-bubble {
            max-width: 70%;
            padding: 0.75rem 1rem;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-bubble {
            background: #0066cc;
            color: white;
        }
        
        .message.assistant .message-bubble {
            background: #f1f3f4;
            color: #333;
        }
        
        .right-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .events-pane {
            flex: 2;
            background: white;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .tools-pane {
            flex: 1;
            background: white;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .events-header, .tools-header {
            padding: 1rem;
            border-bottom: 1px solid #e1e5e9;
            font-weight: 600;
        }
        
        .events-content, .tools-content {
            flex: 1;
            overflow-y: auto;
            padding: 0.5rem;
        }
        
        .event {
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            margin-bottom: 0.5rem;
        }
        
        .event-header {
            padding: 0.75rem;
            background: #f8f9fa;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: monospace;
            font-size: 0.85rem;
        }
        
        .event-header:hover {
            background: #e9ecef;
        }
        
        .tools-content .event-header {
            cursor: default;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .tools-content .event-header.handoff {
            background: #f3e8ff;
            border-left: 4px solid #8b5cf6;
        }
        
        .tools-content .event-header.tool {
            background: #fef3e2;
            border-left: 4px solid #f59e0b;
        }
        
        .event-content {
            padding: 0.75rem;
            background: white;
            border-top: 1px solid #e1e5e9;
            font-family: monospace;
            font-size: 0.8rem;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .event-content.collapsed {
            display: none;
        }
        
        .controls {
            padding: 1rem;
            border-top: 1px solid #e1e5e9;
            background: #f8f9fa;
            display: flex;
            gap: 0.5rem;
        }
        
        .mute-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .mute-btn.unmuted {
            background: #28a745;
            color: white;
        }
        
        .mute-btn.muted {
            background: #dc3545;
            color: white;
        }
        
        .mute-btn.active {
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .status {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .connected {
            color: #28a745;
        }
        
        .disconnected {
            color: #dc3545;
        }

        .contract-confirmation {
            background: white;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 2px solid #0066cc;
            box-shadow: 0 4px 12px rgba(0, 102, 204, 0.2);
        }
        
        .contract-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #e1e5e9;
            border-radius: 6px 6px 0 0;
        }
        
        .contract-header h3 {
            margin: 0;
            color: #0066cc;
            font-size: 1.1rem;
        }
        
        .contract-header p {
            margin: 0.5rem 0 0 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .contract-details {
            padding: 1rem;
        }
        
        .contract-field {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .contract-field label {
            font-weight: 600;
            color: #333;
        }
        
        .contract-field span {
            color: #0066cc;
            font-weight: 500;
        }
        
        .contract-actions {
            padding: 1rem;
            border-top: 1px solid #e1e5e9;
            display: flex;
            gap: 0.5rem;
        }
        
        .confirm-btn {
            flex: 1;
            padding: 0.75rem;
            border: none;
            border-radius: 6px;
            background: #28a745;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .confirm-btn:hover:not(:disabled) {
            background: #218838;
        }
        
        .confirm-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .reject-btn {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #dc3545;
            border-radius: 6px;
            background: white;
            color: #dc3545;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .reject-btn:hover {
            background: #dc3545;
            color: white;
        }
        
        .contract-confirmed {
            padding: 2rem;
            text-align: center;
            background: #d4edda;
            border-radius: 6px;
        }
        
        .contract-confirmed h3 {
            margin: 0;
            color: #155724;
        }
        
        .contract-confirmed p {
            margin: 0.5rem 0 0 0;
            color: #155724;
        }
        
        .contract-error {
            padding: 2rem;
            text-align: center;
            background: #f8d7da;
            border-radius: 6px;
        }
        
        .contract-error h3 {
            margin: 0;
            color: #721c24;
        }
        
        .contract-error p {
            margin: 0.5rem 0 1rem 0;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Realtime Demo</h1>
        <button id="connectBtn" class="connect-btn disconnected">Connect</button>
    </div>
    
    <div class="main">
        <div class="messages-pane">
            <div class="messages-header">
                Conversation
            </div>
            <div id="messagesContent" class="messages-content">
                <!-- Messages will appear here -->
            </div>
            <div class="controls">
                <button id="muteBtn" class="mute-btn unmuted" disabled>🎤 Mic On</button>
                <span id="status" class="status disconnected">Disconnected</span>
            </div>
        </div>
        
        <div class="right-column">
            <div class="events-pane">
                <div class="events-header">
                    Event stream
                </div>
                <div id="eventsContent" class="events-content">
                    <!-- Events will appear here -->
                </div>
            </div>
            
            <div class="tools-pane">
                <div class="tools-header">
                    Tools & Handoffs
                </div>
                <div id="toolsContent" class="tools-content">
                    <!-- Tools and handoffs will appear here -->
                </div>
            </div>
        </div>
    </div>

    <script src="/static/app.js"></script>
</body>
</html>
class RealtimeDemo {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.isMuted = false;
        this.isCapturing = false;
        this.audioContext = null;
        this.processor = null;
        this.stream = null;
        this.sessionId = this.generateSessionId();
        
        // Audio playback queue
        this.audioQueue = [];
        this.isPlayingAudio = false;
        this.playbackAudioContext = null;
        this.currentAudioSource = null;
        
        this.initializeElements();
        this.setupEventListeners();
    }
    
    initializeElements() {
        this.connectBtn = document.getElementById('connectBtn');
        this.muteBtn = document.getElementById('muteBtn');
        this.status = document.getElementById('status');
        this.messagesContent = document.getElementById('messagesContent');
        this.eventsContent = document.getElementById('eventsContent');
        this.toolsContent = document.getElementById('toolsContent');
    }
    
    setupEventListeners() {
        this.connectBtn.addEventListener('click', () => {
            if (this.isConnected) {
                this.disconnect();
            } else {
                this.connect();
            }
        });
        
        this.muteBtn.addEventListener('click', () => {
            this.toggleMute();
        });
    }
    
    generateSessionId() {
        return 'session_' + Math.random().toString(36).substr(2, 9);
    }
    
    async connect() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            this.ws = new WebSocket(`${protocol}//${host}/ws/${this.sessionId}`);
            
            this.ws.onopen = () => {
                this.isConnected = true;
                this.updateConnectionUI();
                this.startContinuousCapture();
            };
            
            this.ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleRealtimeEvent(data);
            };
            
            this.ws.onclose = () => {
                this.isConnected = false;
                this.updateConnectionUI();
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
            };
            
        } catch (error) {
            console.error('Failed to connect:', error);
        }
    }
    
    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
        this.stopContinuousCapture();
    }
    
    updateConnectionUI() {
        if (this.isConnected) {
            this.connectBtn.textContent = 'Disconnect';
            this.connectBtn.className = 'connect-btn connected';
            this.status.textContent = 'Connected';
            this.status.className = 'status connected';
            this.muteBtn.disabled = false;
        } else {
            this.connectBtn.textContent = 'Connect';
            this.connectBtn.className = 'connect-btn disconnected';
            this.status.textContent = 'Disconnected';
            this.status.className = 'status disconnected';
            this.muteBtn.disabled = true;
        }
    }
    
    toggleMute() {
        this.isMuted = !this.isMuted;
        this.updateMuteUI();
    }
    
    updateMuteUI() {
        if (this.isMuted) {
            this.muteBtn.textContent = '🔇 Mic Off';
            this.muteBtn.className = 'mute-btn muted';
        } else {
            this.muteBtn.textContent = '🎤 Mic On';
            this.muteBtn.className = 'mute-btn unmuted';
            if (this.isCapturing) {
                this.muteBtn.classList.add('active');
            }
        }
    }
    
    async startContinuousCapture() {
        if (!this.isConnected || this.isCapturing) return;
        
        // Check if getUserMedia is available
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            throw new Error('getUserMedia not available. Please use HTTPS or localhost.');
        }
        
        try {
            this.stream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    sampleRate: 24000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                } 
            });
            
            this.audioContext = new AudioContext({ sampleRate: 24000 });
            const source = this.audioContext.createMediaStreamSource(this.stream);
            
            // Create a script processor to capture audio data
            this.processor = this.audioContext.createScriptProcessor(4096, 1, 1);
            source.connect(this.processor);
            this.processor.connect(this.audioContext.destination);
            
            this.processor.onaudioprocess = (event) => {
                if (!this.isMuted && this.ws && this.ws.readyState === WebSocket.OPEN) {
                    const inputBuffer = event.inputBuffer.getChannelData(0);
                    const int16Buffer = new Int16Array(inputBuffer.length);
                    
                    // Convert float32 to int16
                    for (let i = 0; i < inputBuffer.length; i++) {
                        int16Buffer[i] = Math.max(-32768, Math.min(32767, inputBuffer[i] * 32768));
                    }
                    
                    this.ws.send(JSON.stringify({
                        type: 'audio',
                        data: Array.from(int16Buffer)
                    }));
                }
            };
            
            this.isCapturing = true;
            this.updateMuteUI();
            
        } catch (error) {
            console.error('Failed to start audio capture:', error);
        }
    }
    
    stopContinuousCapture() {
        if (!this.isCapturing) return;
        
        this.isCapturing = false;
        
        if (this.processor) {
            this.processor.disconnect();
            this.processor = null;
        }
        
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
        
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }
        
        this.updateMuteUI();
    }
    
    handleRealtimeEvent(event) {
        // Add to raw events pane
        this.addRawEvent(event);
        
        // NEW: Handle contract confirmation required event
        if (event.type === 'contract_confirmation_required') {
            console.log('DEBUG: Contract confirmation required event received:', event);
            this.showContractConfirmation(event);
            return;
        }
        
        // Handle contract execution results
        if (event.type === 'contract_executed') {
            this.handleContractExecuted(event);
            return;
        }
        
        if (event.type === 'contract_cancelled') {
            this.handleContractCancelled(event);
            return;
        }
        
        if (event.type === 'contract_error') {
            this.handleContractError(event);
            return;
        }
        
        // Add to tools panel if it's a tool or handoff event
        if (event.type === 'tool_start' || event.type === 'tool_end' || event.type === 'handoff') {
            this.addToolEvent(event);
        }
        
        // Handle specific event types
        switch (event.type) {
            case 'audio':
                this.playAudio(event.audio);
                break;
            case 'audio_interrupted':
                this.stopAudioPlayback();
                break;
            case 'history_updated':
                this.updateMessagesFromHistory(event.history);
                break;
        }
    }
    
    
    updateMessagesFromHistory(history) {
        console.log('updateMessagesFromHistory called with:', history);
        
        // Clear all existing messages
        this.messagesContent.innerHTML = '';
        
        if (history && Array.isArray(history)) {
            console.log('Processing history array with', history.length, 'items');
            
            history.forEach((item, index) => {
                console.log(`History item ${index}:`, item);
                
                if (item.type === 'message') {
                    const role = item.role;
                    console.log(`Message item - role: ${role}, content:`, item.content);
                    
                    let content = '';
                    if (Array.isArray(item.content)) {
                        item.content.forEach(contentPart => {
                            console.log('Content part:', contentPart);
                            if (contentPart.type === 'text' && contentPart.text) {
                                content += contentPart.text;
                            } else if (contentPart.type === 'input_text' && contentPart.text) {
                                content += contentPart.text;
                            } else if (contentPart.type === 'input_audio' && contentPart.transcript) {
                                content += contentPart.transcript;
                            } else if (contentPart.type === 'audio' && contentPart.transcript) {
                                content += contentPart.transcript;
                            }
                        });
                    } else if (typeof item.content === 'string') {
                        content = item.content;
                    }
                    
                    console.log(`Final content for ${role}:`, content);
                    
                    if (content.trim()) {
                        this.addMessage(role, content.trim());
                        console.log(`Added message: ${role} - ${content.trim()}`);
                    }
                } else {
                    console.log(`Skipping non-message item of type: ${item.type}`);
                }
            });
        } else {
            console.log('History is not an array or is null/undefined');
        }
        
        this.scrollToBottom();
    }
    
    addMessage(role, content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        
        const bubbleDiv = document.createElement('div');
        bubbleDiv.className = 'message-bubble';
        bubbleDiv.textContent = content;
        
        messageDiv.appendChild(bubbleDiv);
        this.messagesContent.appendChild(messageDiv);
    }
    
    addRawEvent(event) {
        const eventDiv = document.createElement('div');
        eventDiv.className = 'event';
        
        const headerDiv = document.createElement('div');
        headerDiv.className = 'event-header';
        headerDiv.textContent = `${event.type} - ${JSON.stringify(event).substring(0, 50)}...`;
        headerDiv.onclick = () => {
            const content = eventDiv.querySelector('.event-content');
            content.classList.toggle('collapsed');
        };
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'event-content collapsed';
        contentDiv.textContent = JSON.stringify(event, null, 2);
        
        eventDiv.appendChild(headerDiv);
        eventDiv.appendChild(contentDiv);
        
        this.eventsContent.appendChild(eventDiv);
        this.eventsContent.scrollTop = this.eventsContent.scrollHeight;
    }
    
    addToolEvent(event) {
        let title = '';
        let description = '';
        let eventClass = '';
        
        if (event.type === 'handoff') {
            title = `🔄 Handoff`;
            description = `From ${event.from} to ${event.to}`;
            eventClass = 'handoff';
        } else if (event.type === 'tool_start') {
            title = `🔧 Tool Started`;
            description = `Running ${event.tool}`;
            eventClass = 'tool';
        } else if (event.type === 'tool_end') {
            title = `✅ Tool Completed`;
            description = `${event.tool}: ${event.output || 'No output'}`;
            eventClass = 'tool';
        }
        
        const eventDiv = document.createElement('div');
        eventDiv.className = 'event';
        
        const headerDiv = document.createElement('div');
        headerDiv.className = `event-header ${eventClass}`;
        headerDiv.innerHTML = `
            <strong>${title}</strong><br>
            <small>${description}</small>
        `;
        
        eventDiv.appendChild(headerDiv);
        this.toolsContent.appendChild(eventDiv);
        this.toolsContent.scrollTop = this.toolsContent.scrollHeight;
    }
    
    scrollToBottom() {
        this.messagesContent.scrollTop = this.messagesContent.scrollHeight;
    }

    showContractConfirmation(event) {
        console.log('DEBUG: showContractConfirmation called with:', event);
        console.log('DEBUG: Tool args:', event.tool_args);
        
        // Create contract confirmation section
        const contractSection = document.createElement('div');
        contractSection.className = 'contract-confirmation';
        contractSection.innerHTML = `
            <div class="contract-header">
                <h3>📄 Contract Ready for Review</h3>
                <p>Please review the contract details and confirm to proceed:</p>
            </div>
            <div class="contract-details">
                <div class="contract-field">
                    <label>Company:</label>
                    <span id="contract-company">Loading...</span>
                </div>
                <div class="contract-field">
                    <label>Contact Person:</label>
                    <span id="contract-person">Loading...</span>
                </div>
                <div class="contract-field">
                    <label>Recording Seats:</label>
                    <span id="contract-seats">Loading...</span>
                </div>
                <div class="contract-field">
                    <label>Start Date:</label>
                    <span id="contract-start">Loading...</span>
                </div>
                <div class="contract-field">
                    <label>End Date:</label>
                    <span id="contract-end">Loading...</span>
                </div>
                <div class="contract-field">
                    <label>Total Price:</label>
                    <span id="contract-price">Loading...</span>
                </div>
            </div>
            <div class="contract-actions">
                <button id="confirm-contract" class="confirm-btn" disabled>
                    ✅ Confirm & Sign Contract
                </button>
                <button id="reject-contract" class="reject-btn">
                    ❌ Cancel
                </button>
            </div>
        `;

        // Insert before the tools pane
        const rightColumn = document.querySelector('.right-column');
        rightColumn.insertBefore(contractSection, rightColumn.firstChild);

        // Store reference for tool data extraction
        this.currentContractEvent = event;
        this.pendingContractConfirmation = true;

        // Set up event listeners
        document.getElementById('confirm-contract').addEventListener('click', () => {
            this.confirmContract();
        });
        
        document.getElementById('reject-contract').addEventListener('click', () => {
            this.rejectContract();
        });

        // Extract and populate contract details immediately
        this.extractContractDetails();
    }

    extractContractDetails() {
        // Extract from tool arguments - these are now real arguments!
        const toolArgs = this.currentContractEvent.tool_args || {};
        
        console.log('DEBUG: extractContractDetails called');
        console.log('DEBUG: currentContractEvent:', this.currentContractEvent);
        console.log('DEBUG: toolArgs:', toolArgs);
        
        // No delay needed - we have real data immediately
        const companyName = toolArgs.company_name || 'Unknown Company';
        const personName = toolArgs.person_name || 'Unknown Person';
        const seats = (toolArgs.number_of_recording_seats || 0);
        const startDate = toolArgs.contract_start_date || 'Unknown Start Date';
        const endDate = toolArgs.contract_end_date || 'Unknown End Date';
        const price = (toolArgs.contract_price || 0);
        
        console.log('DEBUG: Setting contract details:', {
            companyName, personName, seats, startDate, endDate, price
        });
        
        document.getElementById('contract-company').textContent = companyName;
        document.getElementById('contract-person').textContent = personName;
        document.getElementById('contract-seats').textContent = seats + ' seats';
        document.getElementById('contract-start').textContent = startDate;
        document.getElementById('contract-end').textContent = endDate;
        document.getElementById('contract-price').textContent = '$' + price.toLocaleString();
        
        // Enable confirm button immediately
        document.getElementById('confirm-contract').disabled = false;
    }

    confirmContract() {
        // Send confirmation to backend
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                type: 'contract_confirmation',
                action: 'confirm',
                tool_id: this.currentContractEvent.tool_id || 'contract_1'
            }));
        }

        // Update UI
        const contractSection = document.querySelector('.contract-confirmation');
        contractSection.innerHTML = `
            <div class="contract-confirmed">
                <h3>✅ Contract Confirmed!</h3>
                <p>Generating contract documents and sending for signature...</p>
            </div>
        `;

        // Clean up
        this.pendingContractConfirmation = false;
        this.currentContractEvent = null;

        // Remove after 3 seconds
        setTimeout(() => {
            contractSection.remove();
        }, 3000);
    }

    rejectContract() {
        // Send rejection to backend
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                type: 'contract_confirmation',
                action: 'reject',
                tool_id: this.currentContractEvent.tool_id || 'contract_1'
            }));
        }

        // Remove contract section
        document.querySelector('.contract-confirmation').remove();
        
        // Clean up
        this.pendingContractConfirmation = false;
        this.currentContractEvent = null;
    }

    handleContractExecuted(event) {
        console.log('Contract executed:', event);
        
        // Update existing contract section to show success
        const contractSection = document.querySelector('.contract-confirmation');
        if (contractSection) {
            contractSection.innerHTML = `
                <div class="contract-confirmed">
                    <h3>✅ Contract Executed Successfully!</h3>
                    <p>${event.message}</p>
                </div>
            `;
            
            // Remove after 5 seconds
            setTimeout(() => {
                contractSection.remove();
            }, 5000);
        }
    }

    handleContractCancelled(event) {
        console.log('Contract cancelled:', event);
        
        // Remove contract section immediately
        const contractSection = document.querySelector('.contract-confirmation');
        if (contractSection) {
            contractSection.remove();
        }
        
        // Clean up state
        this.pendingContractConfirmation = false;
        this.currentContractEvent = null;
    }

    handleContractError(event) {
        console.log('Contract error:', event);
        
        // Show error in contract section
        const contractSection = document.querySelector('.contract-confirmation');
        if (contractSection) {
            contractSection.innerHTML = `
                <div class="contract-error">
                    <h3>❌ Contract Error</h3>
                    <p>${event.message}</p>
                    <button onclick="this.parentElement.parentElement.remove()" class="reject-btn">
                        Close
                    </button>
                </div>
            `;
        }
    }

    async playAudio(audioBase64) {
        try {
            if (!audioBase64 || audioBase64.length === 0) {
                console.warn('Received empty audio data, skipping playback');
                return;
            }
            
            // Add to queue
            this.audioQueue.push(audioBase64);
            
            // Start processing queue if not already playing
            if (!this.isPlayingAudio) {
                this.processAudioQueue();
            }
            
        } catch (error) {
            console.error('Failed to play audio:', error);
        }
    }
    
    async processAudioQueue() {
        if (this.isPlayingAudio || this.audioQueue.length === 0) {
            return;
        }
        
        this.isPlayingAudio = true;
        
        // Initialize audio context if needed
        if (!this.playbackAudioContext) {
            this.playbackAudioContext = new AudioContext({ sampleRate: 24000 });
        }
        
        while (this.audioQueue.length > 0) {
            const audioBase64 = this.audioQueue.shift();
            await this.playAudioChunk(audioBase64);
        }
        
        this.isPlayingAudio = false;
    }
    
    async playAudioChunk(audioBase64) {
        return new Promise((resolve, reject) => {
            try {
                // Decode base64 to ArrayBuffer
                const binaryString = atob(audioBase64);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }
                
                // Convert bytes to Int16Array (assuming 16-bit audio)
                const int16Array = new Int16Array(bytes.buffer);
                
                // Convert Int16 to Float32 for Web Audio API
                const float32Array = new Float32Array(int16Array.length);
                for (let i = 0; i < int16Array.length; i++) {
                    float32Array[i] = int16Array[i] / 32768.0;
                }
                
                const audioBuffer = this.playbackAudioContext.createBuffer(1, float32Array.length, 24000);
                audioBuffer.getChannelData(0).set(float32Array);
                
                const source = this.playbackAudioContext.createBufferSource();
                source.buffer = audioBuffer;
                source.connect(this.playbackAudioContext.destination);
                
                // Store reference to current source
                this.currentAudioSource = source;
                
                source.onended = () => {
                    resolve();
                };
                
                source.start();
                
            } catch (error) {
                console.error('Failed to play audio chunk:', error);
                reject(error);
            }
        });
    }
    
    stopAudioPlayback() {
        console.log('Stopping audio playback due to interruption');
        
        // Stop current audio source if playing
        if (this.currentAudioSource) {
            try {
                this.currentAudioSource.stop();
                this.currentAudioSource = null;
            } catch (error) {
                console.error('Error stopping audio source:', error);
            }
        }
        
        // Clear the audio queue
        this.audioQueue = [];
        
        // Reset playback state
        this.isPlayingAudio = false;
        
        console.log('Audio playback stopped and queue cleared');
    }
}

// Initialize the demo when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new RealtimeDemo();
});
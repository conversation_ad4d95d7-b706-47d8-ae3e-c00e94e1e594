from agents import function_tool
from agents.extensions.handoff_prompt import RECOMMENDED_PROMPT_PREFIX
from agents.realtime import RealtimeAgent, realtime_handoff

from faq_tool import faq_lookup_tool
from email_service import email_service

"""
When running the UI example locally, you can edit this file to change the setup. THe server
will use the agent returned from get_starting_agent() as the starting agent."""

### TOOLS
GLYPHIC_AI_SAAS_PLATFORM_DESCRIPTION = """
Glyphic is an AI-powered sales and revenue intelligence platform that acts as a co-pilot for go-to-market teams. It captures, analyzes, and enriches customer interactions (calls, emails, meetings) to improve sales efficiency, deal execution, and coaching.

Core Capabilities:

Conversation Capture & Transcription: Multi-platform, multilingual, CRM-integrated.

AI Summaries & Notes: Auto-generated, customizable (SPICED, MEDDIC, BANT, etc.).

CRM Automation: Real-time syncing of insights (qualification, competitors, sentiment, requests).

Conversational Search ("Ask Glyphic"): Natural language queries across all interactions.

Strategic Analytics: Trends, win/loss drivers, competitor mentions, feature requests.

Coaching & Enablement: Call scoring, improvement areas, playbooks from best practices.

Deal & Pipeline Support: Risk identification, next steps, methodology tracking.

Pre-Call Prep & Follow-ups: Automated prep sheets, follow-up emails, and handovers.

Customization & Memory: Tailors insights, workflows, and prompts to team preferences.

Technical Strengths:

AI-Native: Built with cutting-edge LLMs (Anthropic Claude, OpenAI, proprietary).

Integrations: Deep Salesforce & HubSpot support; email, calendar, Slack; expanding to more.

Security & Compliance: SOC2 Type 2, GDPR compliant, secure regional data hosting.

Mission (Inferred): Empower GTM teams with actionable intelligence from customer interactions.
Vision (Inferred): Be the leading AI co-pilot for GTM teams by unlocking conversational data.
"""


# Global storage for pending contracts (in production, use Redis/database)
_pending_contracts = {}

def trigger_contract_confirmation(session_id: str, tool_args: dict) -> str:
    """Trigger contract confirmation flow with real arguments"""
    import json
    
    # Store the contract details for this session
    _pending_contracts[session_id] = tool_args
    print(f"INTERCEPT: Contract triggered with real args for session {session_id}: {tool_args}")
    
    # Return a special marker that the server can detect
    return f"__CONTRACT_CONFIRMATION_REQUIRED__|{json.dumps(tool_args)}"

def execute_contract_after_confirmation(tool_args: dict) -> str:
    """Actually execute the contract after user confirmation"""
    company_name = tool_args['company_name']
    person_name = tool_args['person_name']
    number_of_recording_seats = tool_args['number_of_recording_seats']
    contract_start_date = tool_args['contract_start_date']
    contract_end_date = tool_args['contract_end_date']
    contract_price = tool_args['contract_price']
    
    print(f"EXECUTING: Contract for {company_name} with {person_name} for {number_of_recording_seats} seats from {contract_start_date} to {contract_end_date} for ${contract_price}")
    
    # Here you would do the real contract creation:
    # - Generate PDF
    # - Send to DocuSign
    # - Create user account
    # - Send emails
    
    return f"✅ Contract executed! Generated contract for {company_name} ({person_name}) - {number_of_recording_seats} recording seats from {contract_start_date} to {contract_end_date} for ${contract_price:,}. Contract sent to {person_name} for digital signature."


# @function_tool(
#     name_override="faq_lookup_tool", description_override="Lookup frequently asked questions."
# )
# async def faq_lookup_tool(question: str) -> str:
#     if "bag" in question or "baggage" in question:
#         return (
#             "You are allowed to bring one bag on the plane. "
#             "It must be under 50 pounds and 22 inches x 14 inches x 9 inches."
#         )
#     elif "seats" in question or "plane" in question:
#         return (
#             "There are 120 seats on the plane. "
#             "There are 22 business class seats and 98 economy seats. "
#             "Exit rows are rows 4 and 16. "
#             "Rows 5-8 are Economy Plus, with extra legroom. "
#         )
#     elif "wifi" in question:
#         return "We have free wifi on the plane, join Airline-Wifi"
#     return "I'm sorry, I don't know the answer to that question."


@function_tool
async def sign_contract(confirmation_number: str, new_seat: str) -> str:
    """
    Update the seat for a given confirmation number.

    Args:
        confirmation_number: The confirmation number for the flight.
        new_seat: The new seat to update to.
    """
    return f"Updated seat to {new_seat} for confirmation number {confirmation_number}"


@function_tool
def get_weather(city: str) -> str:
    """Get the weather in a city."""
    return f"The weather in {city} is sunny."


@function_tool
def send_email(email: str, subject: str = "Welcome to Glyphic AI", content: str = "") -> str:
    """Sends an email to the user with optional subject and content."""
    result = email_service.send_email(email, subject, content)
    if result["success"]:
        return f"Email sent successfully to {email} with subject '{subject}'. {result['message']}"
    else:
        return f"Failed to send email to {email}. Error: {result['message']}"


@function_tool
def send_contract_completion_email(email: str, company_name: str, person_name: str, number_of_recording_seats: int, contract_start_date: str, contract_end_date: str, contract_price: int) -> str:
    """Sends a contract completion email with all contract details and next steps."""
    return _send_contract_completion_email_internal(email, company_name, person_name, number_of_recording_seats, contract_start_date, contract_end_date, contract_price)


@function_tool
def crm_close_deal(deal_name: str) -> str:
    """Closes the deal in the CRM."""
    print(f"Closing deal {deal_name} in CRM.")
    return "Deal closed in CRM."

def _send_contract_completion_email_internal(email: str, company_name: str, person_name: str, number_of_recording_seats: int, contract_start_date: str, contract_end_date: str, contract_price: int) -> str:
    """Internal function to send contract completion email using the email service."""
    result = email_service.send_contract_completion_email(
        email=email,
        company_name=company_name,
        person_name=person_name,
        number_of_recording_seats=number_of_recording_seats,
        contract_start_date=contract_start_date,
        contract_end_date=contract_end_date,
        contract_price=contract_price
    )

    if result["success"]:
        return f"Contract completion email sent successfully to {email} for {company_name}."
    else:
        return f"Failed to send contract completion email to {email}. Error: {result['message']}"


def _calculate_contract_months(start_date: str, end_date: str) -> int:
    """Calculate the number of months between start and end date, accounting for full months."""
    from datetime import datetime
    try:
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')

        # Calculate the difference in months
        months = (end.year - start.year) * 12 + (end.month - start.month)

        # If the end day is greater than or equal to start day, we have a full month
        # For example: 2025-09-01 to 2026-08-31 should be 12 months
        if end.day >= start.day:
            months += 1

        return max(1, months)  # Minimum 1 month
    except Exception as e:
        print(f"Error parsing dates {start_date} to {end_date}: {e}")
        return 12  # Default to 12 months if parsing fails

def _validate_pricing(contract_price: int, number_of_recording_seats: int, contract_start_date: str, contract_end_date: str) -> dict:
    """Validate contract pricing against business rules."""
    months = _calculate_contract_months(contract_start_date, contract_end_date)

    # Calculate price per seat per month
    total_seat_months = number_of_recording_seats * months
    price_per_seat_per_month = contract_price / total_seat_months if total_seat_months > 0 else 0

    # Business rules
    MIN_PRICE_PER_SEAT = 120  # Absolute minimum
    STANDARD_PRICE_PER_SEAT = 140  # Standard price


    if price_per_seat_per_month < MIN_PRICE_PER_SEAT:
        suggested_price = MIN_PRICE_PER_SEAT * total_seat_months
        return {
            "valid": False,
            "error": f"Price too low! Minimum price is ${MIN_PRICE_PER_SEAT}/seat/month. For {number_of_recording_seats} seats over {months} months, minimum total is ${suggested_price:,}.",
            "suggested_price": suggested_price,
            "price_per_seat_per_month": price_per_seat_per_month
        }

    return {
        "valid": True,
        "price_per_seat_per_month": price_per_seat_per_month,
        "months": months,
        "total_seat_months": total_seat_months
    }

@function_tool
<<<<<<< HEAD
def alguna_tool(company_name: str, person_name: str, number_of_recording_seats: int, contract_start_date: str, contract_end_date: str, contract_price: int, email: str = "") -> str:
    """Gets the prospect to sign the contract and sends a completion email. Validates pricing against business rules."""
    print(f"Getting prospect to sign the contract for {company_name} with {person_name} for {number_of_recording_seats} recording seats starting on {contract_start_date} and ending on {contract_end_date} for {contract_price}.")

    # CRITICAL: Validate pricing before proceeding
    pricing_validation = _validate_pricing(contract_price, number_of_recording_seats, contract_start_date, contract_end_date)

    if not pricing_validation["valid"]:
        return f"❌ CONTRACT REJECTED: {pricing_validation['error']} Please negotiate a fair price that meets our minimum requirements."

    # Log the validated pricing
    price_per_seat_per_month = pricing_validation["price_per_seat_per_month"]
    months = pricing_validation["months"]
    print(f"✅ Pricing validated: ${price_per_seat_per_month:.2f}/seat/month for {months} months")

    # Simulate contract signing process
    contract_signed = True

    if contract_signed:
        # If email is provided, send the contract completion email
        if email:
            email_result = _send_contract_completion_email_internal(
                email=email,
                company_name=company_name,
                person_name=person_name,
                number_of_recording_seats=number_of_recording_seats,
                contract_start_date=contract_start_date,
                contract_end_date=contract_end_date,
                contract_price=contract_price
            )
            return f"✅ Contract successfully signed for {company_name} at ${price_per_seat_per_month:.2f}/seat/month! {email_result} Next steps: Account provisioning will begin immediately, and you'll receive login credentials within 24 hours."
        else:
            return f"✅ Contract successfully signed for {company_name} with {person_name} for {number_of_recording_seats} recording seats starting on {contract_start_date} and ending on {contract_end_date} for ${contract_price:,} (${price_per_seat_per_month:.2f}/seat/month). Please provide your email address to receive the welcome email with next steps."
    else:
        return f"There was an issue with the contract signing process. Please try again or contact support."


@function_tool
def calculate_contract_price(number_of_recording_seats: int, contract_length_months: int, price_per_seat_per_month: int = 140) -> str:
    """Calculate contract price with validation against business rules. Use this during price negotiations."""
    MIN_PRICE_PER_SEAT = 120  # Absolute minimum
    STANDARD_PRICE_PER_SEAT = 140  # Standard price

    # Validate the requested price per seat
    if price_per_seat_per_month < MIN_PRICE_PER_SEAT:
        return f"❌ Price ${price_per_seat_per_month}/seat/month is below our minimum of ${MIN_PRICE_PER_SEAT}/seat/month. Cannot proceed with this pricing."

    # Calculate total price
    total_price = number_of_recording_seats * contract_length_months * price_per_seat_per_month

    # Calculate discount information
    if price_per_seat_per_month < STANDARD_PRICE_PER_SEAT:
        discount_per_seat = STANDARD_PRICE_PER_SEAT - price_per_seat_per_month
        total_discount = discount_per_seat * number_of_recording_seats * contract_length_months
        discount_percentage = (discount_per_seat / STANDARD_PRICE_PER_SEAT) * 100

        return f"✅ Contract pricing calculated: {number_of_recording_seats} seats × {contract_length_months} months × ${price_per_seat_per_month}/seat/month = ${total_price:,}. This includes a {discount_percentage:.1f}% discount (saving ${total_discount:,} from standard pricing)."
    else:
        return f"✅ Contract pricing calculated: {number_of_recording_seats} seats × {contract_length_months} months × ${price_per_seat_per_month}/seat/month = ${total_price:,} (standard pricing)."
=======
def alguna_tool(company_name: str, person_name: str, number_of_recording_seats: int, contract_start_date: str, contract_end_date: str, contract_price: int) -> str:
    """Gets the prospect to sign the contract."""
    
    # INTERCEPT HERE - we have real arguments!
    tool_args = {
        'company_name': company_name,
        'person_name': person_name, 
        'number_of_recording_seats': number_of_recording_seats,
        'contract_start_date': contract_start_date,
        'contract_end_date': contract_end_date,
        'contract_price': contract_price
    }
    
    print(f"DEBUG: alguna_tool called with real args: {tool_args}")
    
    # TODO: Get session_id from context (for now, use a placeholder)
    # In a real implementation, you'd pass session_id through the tool context
    session_id = "current_session"  # This needs to be dynamic
    
    # Trigger confirmation instead of executing immediately
    return trigger_contract_confirmation(session_id, tool_args)
>>>>>>> main


@function_tool
def provision_account(company_name: str) -> str:
    """Provision account for the company."""
    print(f"Provisioning account for {company_name}")
    return f"Account provisioned for {company_name}."



faq_agent = RealtimeAgent(
    name="FAQ Agent",
    handoff_description="A helpful agent that can answer questions about Glyphic.",
    instructions=f"""{RECOMMENDED_PROMPT_PREFIX}
    You are an FAQ agent. If you are speaking to a prospect, you probably were transferred to from the sales agent.
    Use the following routine to support the prospect.
    # Routine
    1. Identify the last question asked by the prospect.
    2. Use the faq lookup tool to answer the question. Do not rely on your own knowledge.
    3. If you cannot answer the question, transfer back to the sales agent.""",
    tools=[faq_lookup_tool],
)

alguna_agent = RealtimeAgent(
    name="Alguna contract signing agent",
    handoff_description="A helpful agent that can create and sign a contract.",
    instructions=f"""{RECOMMENDED_PROMPT_PREFIX}
    You are a contract signing agent. If you are speaking to a prospect, you probably were transferred to from the sales agent.

    Follow this routine to complete the contract signing process:

    # CONTRACT SIGNING ROUTINE:
    1. **COLLECT BASIC INFORMATION**: Gather initial details:
       - Company name
       - Contact person's full name
       - Number of recording seats needed
       - Contract start date (suggest starting next Monday if not specified)
       - Contract end date (typically 12 months from start date)
       - Email address for contract completion notification

    2. **PRICING AND NEGOTIATION**: Handle pricing strategically:
       - Standard price is $140/seat/month
       - Do NOT immediately give away the price - build value first
       - Ask: "Are you interested in hearing about our volume discounts?"
       - If they want a discount, ask for seat count and contract length
       - Use the calculate_contract_price tool to determine pricing
       - You can negotiate down to $120/seat/month minimum (never lower!)
       - For 10+ seats: offer up to $130/seat/month
       - For 25+ seats: offer up to $125/seat/month
       - For 50+ seats: offer up to $120/seat/month
       - Always frame discounts as "special pricing" or "volume discounts"

    3. **PRICE VALIDATION**: Before finalizing:
       - Use calculate_contract_price tool to validate any negotiated price
       - Ensure the price never goes below $120/seat/month
       - Calculate the total contract value clearly

    4. **CONFIRM DETAILS**: Review all information with the prospect to ensure accuracy

    5. **EXECUTE CONTRACT**: Use the alguna_tool to process the contract with all collected information
       - The tool will automatically validate pricing and reject if too low

    6. **COMPLETION**: Once the contract is signed:
       - Confirm the contract completion email has been sent
       - Inform them about next steps (account provisioning, login credentials, onboarding)
       - Let them know the Customer Success team will be in touch within 24 hours
       - Transfer back to the sales agent for final wrap-up

    # IMPORTANT NOTES:
    - Always collect the email address - it's required for the completion process
    - Be thorough in collecting all information before proceeding with contract signing
    - If the customer asks questions not related to contract signing, transfer back to the sales agent
    - Maintain a professional and helpful tone throughout the process
    """,
    tools=[alguna_tool, calculate_contract_price],
)




sales_agent = RealtimeAgent(
    name="Sales Agent",
    handoff_description=
    "A sales agent whose job is to sell Glyphic AIs SaaS platform to people.",
    instructions=
    (f"{RECOMMENDED_PROMPT_PREFIX} "
     "You are a helpful AI account executive for Glyphic AI who works in ENGLISH. Follow this conversation flow:\n\n"
     
     "# CONVERSATION FLOW:\n"
     "1. **WARM OPENING**: Start with friendly small talk to build rapport. Ask about their day, company, or industry trends.\n\n"
     
     "2. **DISCOVERY PHASE**: Focus on discovery questions about their current challenges:\n"
     "   - Ask how much time their sales team currently spends on manual tasks\n"
     "   - Explore how these manual processes impede their sales team's ability to focus on selling\n"
     "   - Understand their current pain points with data entry, note-taking, CRM updates, etc.\n"
     "   - Listen actively and ask follow-up questions to uncover specific challenges\n\n"
     
     "3. **PERSONALIZED PITCH**: Based on their responses, deliver a tailored pitch showing how Glyphic AI solves their specific problems:\n"
     f"   {GLYPHIC_AI_SAAS_PLATFORM_DESCRIPTION}\n"
     "   - Connect their pain points directly to Glyphic's solutions\n"
     "   - Emphasize time savings, efficiency gains, and revenue impact\n"
     "   - Use specific examples relevant to their situation\n\n"
     
     "4. **CLOSING**: Ask if they're ready to take the next step and supercharge their revenue with Glyphic AI. Remember that you can use contract agent to sign the contract.\n\n"
     
     "5. **CONTRACT HANDOFF**: If they show interest in moving forward, transfer them to the alguna agent for contracting. "
     "Tell them once the contract is signed, you will provision the account and send them the login details. \n\n"
     
     "6. **WARM CLOSE**: End conversations warmly and let them know the Customer Success team will get in touch with them shortly if there are any other questions.\n\n"
     
     "# ADDITIONAL GUIDELINES:\n"
     "- Always be consultative, not pushy\n"
     "- Ask permission before transitioning between phases\n"
     "- If they have specific questions about Glyphic, transfer to the FAQ agent\n"
     "- Use your tools to send follow-up emails and close deals in the CRM when appropriate\n"
     "- Keep the conversation natural and conversational\n"
     "REMEMBER: Always communicate in ENGLISH"
     ),
    handoffs=[faq_agent, realtime_handoff(alguna_agent)],
    tools=[send_email, send_contract_completion_email, crm_close_deal, provision_account],
)

faq_agent.handoffs.append(sales_agent)
alguna_agent.handoffs.append(sales_agent)


def get_starting_agent() -> RealtimeAgent:
    return sales_agent